<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.4052 49.8047L0.466187 119.137C-0.734334 122.803 0.431052 125.605 3.41787 127.748L92.0453 179.035C105.855 187.447 118.976 187.805 131.325 179.159L197.366 137.604C200.119 135.799 201.038 133.044 198.55 128.497L177.498 65.9207L28.4052 49.8047Z" fill="#AECAE4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M177.497 65.918L122.906 102.619L127.245 181.671C128.613 180.94 129.973 180.102 131.325 179.156L197.366 137.602C200.119 135.797 201.038 133.042 198.55 128.494L177.497 65.918Z" fill="url(#paint0_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M122.861 101.762L106.091 100.806L100.745 183.296C109.912 186.655 118.755 186.214 127.247 181.677L122.861 101.762ZM100.712 183.284L100.235 183.106C100.395 183.166 100.554 183.226 100.712 183.284L100.745 183.296L100.712 183.284ZM99.6828 182.89L100.18 183.083C100.014 183.021 99.8484 182.956 99.6828 182.89ZM98.6313 182.456L99.128 182.665C98.9625 182.596 98.7969 182.526 98.6313 182.456ZM98.5569 182.425C98.3975 182.356 98.238 182.286 98.0766 182.216L98.5569 182.425ZM97.5074 181.957L98.0021 182.182C97.8366 182.108 97.6709 182.033 97.5074 181.957ZM97.0107 181.727L97.5033 181.957L97.0107 181.727ZM96.9361 181.69C96.7768 181.615 96.6195 181.539 96.4601 181.462L96.9361 181.69ZM127.363 181.615L127.299 181.648L127.44 181.574L127.363 181.615ZM127.597 181.487L127.547 181.514L127.686 181.437L127.597 181.487ZM95.8888 181.179L96.371 181.417C96.2096 181.338 96.0503 181.259 95.8888 181.179ZM127.831 181.359L127.796 181.379L127.936 181.299L127.831 181.359ZM128.067 181.226L128.042 181.241L128.189 181.156L128.067 181.226ZM95.3754 180.916L95.8412 181.154L95.3754 180.916ZM128.301 181.092L128.286 181.1L128.468 180.994L128.301 181.092ZM128.775 180.814C128.694 180.864 128.611 180.912 128.533 180.957L128.775 180.814ZM94.8332 180.63L95.3112 180.881C95.1518 180.798 94.9926 180.715 94.8332 180.63ZM129 180.678L128.858 180.765L129.019 180.667L129 180.678ZM129.234 180.537L129.118 180.607L129.265 180.518L129.234 180.537ZM94.2639 180.322L94.7399 180.578C94.5806 180.493 94.4212 180.409 94.2639 180.322ZM129.468 180.392L129.369 180.452L129.51 180.365L129.468 180.392ZM129.7 180.245L129.617 180.299L129.756 180.212L129.7 180.245ZM93.7237 180.019L94.1915 180.282L93.7237 180.019ZM129.934 180.098L129.864 180.141L130 180.055L129.934 180.098ZM93.1979 179.719L93.6823 179.997C93.5208 179.906 93.3593 179.812 93.1979 179.719ZM130.166 179.947L130.108 179.984L130.246 179.893L130.166 179.947ZM130.398 179.794L130.352 179.825L130.493 179.73L130.398 179.794ZM92.6288 179.386L93.1048 179.665C92.9454 179.574 92.7881 179.481 92.6288 179.386ZM130.631 179.639L130.596 179.661L130.743 179.562L130.631 179.639ZM130.863 179.481L130.841 179.498L131.004 179.386L130.863 179.481ZM92.5439 179.336C92.4611 179.287 92.3783 179.237 92.2955 179.187C92.2127 179.138 92.1299 179.088 92.0471 179.038L92.5439 179.336ZM131.083 179.33L131.327 179.163L131.095 179.324L131.083 179.33Z" fill="url(#paint1_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M106.122 100.295L28.4053 49.8047L0.466198 119.137C-0.734324 122.803 0.431 125.605 3.41781 127.748L92.0453 179.035C94.9763 180.819 97.8761 182.241 100.743 183.292L106.122 100.295Z" fill="url(#paint2_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.8178 46.8327C50.8836 39.3533 69.6281 28.822 88.0888 15.596C91.7184 13.3272 97.9978 14.2099 102.267 16.2603L174.71 61.135C179.193 65.1123 177.799 70.4878 175.596 73.8605C165.388 89.4903 129.436 105.138 121.675 106.194C119.798 106.936 113.27 107.072 107.536 105.016L30.7104 55.9159C27.3807 52.8879 26.3699 49.8606 31.8178 46.8327Z" fill="#818CF8"/>
<g filter="url(#filter0_i)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M123.657 85.2859C128.083 83.5496 142.784 74.895 147.07 71.8244C147.912 71.2977 149.089 71.6782 150.081 72.1542L151.219 72.67C152.26 73.5933 152.182 74.9816 151.671 75.7646C149.301 79.3932 130.605 89.2355 128.803 89.4808C128.367 89.6529 126.763 89.6341 125.432 89.1566L123.505 87.8509C122.732 87.1479 122.392 85.9888 123.657 85.2859Z" fill="white"/>
</g>
<g filter="url(#filter1_i)">
<path d="M121.18 57.4427C124.631 57.0068 127.489 56.4315 129.846 55.7155L131.527 58.4748C128.292 59.4703 124.605 60.1762 120.421 60.5638C119.264 63.0095 117.195 65.2317 114.214 67.1712C111.187 69.141 107.761 70.4947 103.892 71.1442C100.021 71.8528 96.1213 71.8215 92.239 71.0199C88.3553 70.2775 84.8083 68.8489 81.5968 66.7934C78.3852 64.7379 76.1423 62.4608 74.8693 59.9028C73.5964 57.3449 73.469 54.8589 74.5804 52.3842C75.6917 49.9095 77.7158 47.6583 80.7896 45.6582C83.8634 43.6581 87.3349 42.3333 91.2507 41.6536C95.1679 40.9146 99.021 40.9763 102.858 41.749C106.74 42.5506 110.287 43.9792 113.499 46.0347C116.168 47.7428 118.101 49.5209 119.434 51.4561C120.721 53.3622 121.318 55.3674 121.18 57.4427ZM105.973 60.2734C109.258 59.0698 112.349 58.343 115.381 58.1798C115.682 55.0063 113.503 51.9286 108.935 49.0045C106.311 47.3254 103.539 46.152 100.662 45.5132C97.7871 44.8152 94.9438 44.7386 92.0884 45.1954C89.2331 45.6522 86.6876 46.6079 84.4987 48.0322C81.2386 50.1535 79.5765 52.6363 79.604 55.4792C79.6328 58.2628 81.8185 61.0443 86.1609 63.8235C90.3676 66.5159 94.6956 67.9034 99.0543 67.9279C103.458 67.9815 107.337 66.9172 110.644 64.7656C112.227 63.7352 113.446 62.5918 114.255 61.3063C111.999 61.6654 109.922 62.1995 108.023 62.968L105.973 60.2734Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_i" x="122.815" y="71.5698" width="31.2206" height="20.0101" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter1_i" x="73.824" y="41.1329" width="59.7031" height="32.517" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<linearGradient id="paint0_linear" x1="163.762" y1="162.831" x2="141.417" y2="100.002" gradientUnits="userSpaceOnUse">
<stop stop-color="#7177B8"/>
<stop offset="1" stop-color="#B5B8D9"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="111.8" y1="183.882" x2="115.321" y2="113.173" gradientUnits="userSpaceOnUse">
<stop stop-color="#7177B8"/>
<stop offset="1" stop-color="#B4B8E6"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="65.8201" y1="88.3973" x2="44.1591" y2="149.369" gradientUnits="userSpaceOnUse">
<stop stop-color="#A1A7E3"/>
<stop offset="1" stop-color="#7177B8"/>
</linearGradient>
<clipPath id="clip0">
<rect width="200" height="200" fill="white"/>
</clipPath>
</defs>
</svg>
