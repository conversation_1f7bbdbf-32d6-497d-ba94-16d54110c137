.wrong {
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

.word-sound {
  position: absolute;
  width: 40px;
  height: 40px;
  transform: translateY(calc(-50% - 23px));
  right: -55px;
  cursor: pointer;
  fill: theme('colors.gray.600');
}
.word-sound .prefix__icon {
  width: 40px;
  height: 40px;
}
.dark .word-sound {
  fill: theme('colors.gray.50');
  opacity: 0.8;
}
