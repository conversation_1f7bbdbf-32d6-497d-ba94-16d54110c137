[{"name": "fs.Dir", "trans": ["类，表示目录流。创建者fs.opendir()，fs.opendirSync()或 fsPromises.opendir()。"]}, {"name": "fs.<PERSON><PERSON>", "trans": ["类，通过从读取返回的目录条目的表示形式，可以是目录中的文件或子目录fs.Dir。目录条目是文件名和文件类型对的组合。"]}, {"name": "fs.FSWatcher", "trans": ["类，成功调用fs.watch()方法将返回一个新fs.FSWatcher 对象。只要修改了特定的监视文件，所有fs.FSWatcher对象都会发出一个'change'事件。"]}, {"name": "fs.StatWatcher", "trans": ["类，成功调用fs.watchFile()方法将返回一个新fs.StatWatcher 对象。  "]}, {"name": "fs.ReadStream", "trans": ["类，使用的实例fs.ReadStream创建并返回 fs.createReadStream()。"]}, {"name": "fs.Stats", "trans": ["类，fs.Stats对象提供有关文件的信息。"]}, {"name": "fs.WriteStream", "trans": ["类，使用的实例fs.WriteStream创建并返回 fs.createWriteStream()。"]}, {"name": "fs.access(path[, mode], callback)", "trans": ["测试用户对所指定的文件或目录的权限path。该mode参数是一个可选整数，它指定要执行的可访问性检查。检查“文件访问常量”中可能的值mode。可以创建由两个或多个值（例如fs.constants.W_OK | fs.constants.R_OK）的按位或组成的掩码。    "]}, {"name": "fs.accessSync(path[, mode])", "trans": ["同步测试用户对所指定的文件或目录的权限path。该mode参数是一个可选整数，它指定要执行的可访问性检查。"]}, {"name": "fs.appendFile(path, data[, options], callback)", "trans": ["异步将数据追加到文件，如果该文件尚不存在，则创建该文件。data可以是字符串或Buffer。    "]}, {"name": "fs.appendFileSync(path, data[, options])", "trans": ["将数据同步追加到文件，如果该文件尚不存在，则创建该文件。data可以是字符串或Buffer。"]}, {"name": "fs.chmod(path, mode, callback)", "trans": ["异步更改文件的权限"]}, {"name": "fs.chmodSync(path, mode)", "trans": ["同步更改文件的权限"]}, {"name": "fs.chown(path, uid, gid, callback)", "trans": ["异步更改文件的所有者和组。"]}, {"name": "fs.chownSync(path, uid, gid)", "trans": ["同步更改文件的所有者和组。"]}, {"name": "fs.close(fd, callback)", "trans": ["异步关闭文件"]}, {"name": "fs.closeSync(fd)", "trans": ["同步关闭文件"]}, {"name": "fs.constants", "trans": ["返回一个包含文件系统操作常用常量的对象。当前定义的特定常数在FS常数中进行了描述 。"]}, {"name": "fs.copyFile(src, dest[, mode], callback)", "trans": ["异步复制src到dest。默认情况下，dest如果已经存在，则被覆盖。除可能的异常外，没有其他任何参数被赋予回调函数。Node.js不保证复制操作的原子性。如果在打开目标文件进行写入后发生错误，Node.js将尝试删除目标。mode是一个可选整数，它指定复制操作的行为。可以创建由两个或多个值（例如fs.constants.COPYFILE_EXCL | fs.constants.COPYFILE_FICLONE）的按位或组成的掩码 。"]}, {"name": "fs.copyFileSync(src, dest[, mode])", "trans": ["同步复制src到dest。默认情况下，dest如果已经存在，则被覆盖。返回undefined。Node.js不保证复制操作的原子性。如果在打开目标文件进行写入后发生错误，Node.js将尝试删除目标。mode是一个可选整数，它指定复制操作的行为。可以创建由两个或多个值（例如fs.constants.COPYFILE_EXCL | fs.constants.COPYFILE_FICLONE）的按位或组成的掩码 。"]}, {"name": "fs.createReadStream(path[, options])", "trans": ["创建可读流，此方法返回的流具有 64 kb 的默认 highWaterMark"]}, {"name": "fs.createWriteStream(path[, options])", "trans": ["创建可写流"]}, {"name": "fs.exists(path, callback)", "trans": ["弃用: 改为使用 fs.stat() 或 fs.access()。"]}, {"name": "fs.existsSync(path)", "trans": ["如果路径存在，则返回 true，否则返回 false。"]}, {"name": "fs.fchmod(fd, mode, callback)", "trans": ["异步的fchmod(2), 会依参数 mode 权限来更改参数fildes所指文件的权限"]}, {"name": "fs.fchmodSync(fd, mode)", "trans": ["同步fchmod(2), 会依参数mode权限来更改参数fildes所指文件的权限。  "]}, {"name": "fs.fchown(fd, uid, gid, callback)", "trans": ["异步的fchown(2), 会将参数fd指定文件的所有者变更为参数owner代表的用户，而将该文件的组变更为参数group组"]}, {"name": "fs.fchownSync(fd, uid, gid)", "trans": ["同步的fchown(2), 会将参数fd指定文件的所有者变更为参数owner代表的用户，而将该文件的组变更为参数group组"]}, {"name": "fs.fdatasync(fd, callback)", "trans": ["异步的fdatasync(2), 用来刷新数据到磁盘"]}, {"name": "fs.fdatasyncSync(fd)", "trans": ["同步的fdatasync(2), 用来刷新数据到磁盘 "]}, {"name": "fs.fstat(fd[, options], callback)", "trans": ["异步的fstat(2)。回调获得两个参数(err, stats)where stats是fs.Stats对象。fstat()与相同stat()，除了要声明的文件由文件描述符指定fd。"]}, {"name": "fs.fstatSync(fd[, options])", "trans": ["同步的 fstat(2)。"]}, {"name": "fs.fsync(fd, callback)", "trans": ["异步的 fsync(2), 同步内存中所有已修改的文件数据到储存设备"]}, {"name": "fs.fsyncSync(fd)", "trans": ["同步的 fsync(2)， 同步内存中所有已修改的文件数据到储存设备"]}, {"name": "fs.ftruncate(fd[, len], callback)", "trans": ["异步的 ftruncate(2), 参数fd指定的文件大小改为参数length指定的大小"]}, {"name": "fs.ftruncateSync(fd[, len])", "trans": ["ftruncateSync, 参数fd指定的文件大小改为参数length指定的大小"]}, {"name": "fs.futimes(fd, atime, mtime, callback)", "trans": ["更改文件描述符指向的对象的文件系统时间戳。"]}, {"name": "fs.futimesSync(fd, atime, mtime)", "trans": ["fs.futimes() 的同步版本"]}, {"name": "fs.lchmod(path, mode, callback)", "trans": ["异步的 lchmod(2), 用于修改连接文件权限"]}, {"name": "fs.lchmodSync(path, mode)", "trans": ["同步的 lchmod(2),用于修改连接文件权限"]}, {"name": "fs.lchown(path, uid, gid, callback)", "trans": ["异步的 lchown(2),函数改变文件的所有者以及组。如果命名文件是符号链接，则lchown（）会更改链接本身的所有者或组，而不是链接所指向的对象。先前所有者或主要组对该对象的权限被撤消。"]}, {"name": "fs.lchownSync(path, uid, gid)", "trans": ["同步的 lchown(2), 函数改变文件的所有者以及组。如果命名文件是符号链接，则lchown（）会更改链接本身的所有者或组，而不是链接所指向的对象。先前所有者或主要组对该对象的权限被撤消。"]}, {"name": "fs.lutimes(path, atime, mtime, callback)", "trans": ["异步的更改文件时间戳, lutimes（）以与utimes（2）相同的方式更改文件的访问和修改时间，不同之处在于，如果filename引用符号链接，则不会取消引用该链接：相反，符号链接的时间戳为改变了。"]}, {"name": "fs.lutimesSync(path, atime, mtime)", "trans": ["同步的更改文件时间戳，lutimes（）以与utimes（2）相同的方式更改文件的访问和修改时间，不同之处在于，如果filename引用符号链接，则不会取消引用该链接：相反，符号链接的时间戳为改变了。"]}, {"name": "fs.link(existingPath, newPath, callback)", "trans": ["异步的 link(2), 把一个或多个目标文件链接成可执行文件。"]}, {"name": "fs.linkSync(existingPath, newPath)", "trans": ["同步的 link(2), 把一个或多个目标文件链接成可执行文件。"]}, {"name": "fs.lstat(path[, options], callback)", "trans": ["异步的 lstat(2), 获取文件状态"]}, {"name": "fs.lstatSync(path[, options])", "trans": ["同步的 lstat(2), 获取文件状态"]}, {"name": "fs.mkdir(path[, options], callback)", "trans": ["异步地创建目录"]}, {"name": "fs.mkdirSync(path[, options])", "trans": ["同步地创建目录。也可参见 mkdir(2)。"]}, {"name": "fs.mkdtemp(prefix[, options], callback)", "trans": ["创建一个唯一的临时目录。"]}, {"name": "fs.mkdtempSync(prefix[, options])", "trans": ["同步的创建一个唯一的临时目录。"]}, {"name": "fs.open(path[, flags[, mode]], callback)", "trans": ["异步地打开文件。 参见 open(2)。mode 用于设置文件模式（权限和粘滞位），但仅限于创建文件时。 在 Windows 上，只能操作写权限，参见 fs.chmod()。"]}, {"name": "fs.opendir(path[, options], callback)", "trans": ["异步地打开目录。参见 opendir(3)。 "]}, {"name": "fs.opendirSync(path[, options])", "trans": ["同步地打开目录。参见 opendir(3)。 "]}, {"name": "fs.openSync(path[, flags, mode])", "trans": ["同步的打开文件，参见 open(2)."]}, {"name": "fs.read(fd, buffer, offset, length, position, callback)", "trans": ["从 fd 指定的文件中读取数据。buffer 是数据（从 fd 读取）要被写入的 buffer。offset 是 buffer 中开始写入的偏移量。length 是整数，指定要读取的字节数。position 参数指定从文件中开始读取的位置。 如果 position 为 null，则从当前文件位置读取数据，并更新文件位置。 如果 position 是整数，则文件位置会保持不变。"]}, {"name": "fs.read(fd, [options,] callback)", "trans": ["同fs.read，会接受一个同步的fs对象"]}, {"name": "fs.readdir(path[, options], callback)", "trans": ["异步的 readdir(3)。 读取目录的内容。"]}, {"name": "fs.readdirSync(path[, options])", "trans": ["readdir(3)。 读取目录的内容。"]}, {"name": "fs.readFile(path[, options], callback)", "trans": ["异步地读取文件的全部内容。    "]}, {"name": "fs.readFileSync(path[, options])", "trans": ["同步地读取文件的全部内容。"]}, {"name": "fs.readlink(path[, options], callback)", "trans": ["异步的 readlink(2), 读取符号链接的值。"]}, {"name": "fs.readlinkSync(path[, options])", "trans": ["同步的 readlink(2), 读取符号链接的值 "]}, {"name": "fs.readSync(fd, buffer, offset, length, position)", "trans": ["fs.read()，从 fd 指定的文件中读取数据。buffer 是数据（从 fd 读取）要被写入的 buffer。offset 是 buffer 中开始写入的偏移量。length 是整数，指定要读取的字节数。position 参数指定从文件中开始读取的位置。 如果 position 为 null，则从当前文件位置读取数据，并更新文件位置。 如果 position 是整数，则文件位置会保持不变。"]}, {"name": "fs.readSync(fd, buffer, [options])", "trans": ["同fs.read，会接受一个同步的fs对象"]}, {"name": "fs.readv(fd, buffers[, position], callback)", "trans": ["异步的读取数据到多个缓冲区"]}, {"name": "fs.readvSync(fd, buffers[, position])", "trans": ["同步的读取数据到多个缓冲区"]}, {"name": "fs.realpath(path[, options], callback)", "trans": ["异步的通过解析 .、 .. 和符号链接异步地计算规范路径名。"]}, {"name": "fs.realpath.native(path[, options], callback)", "trans": ["异步的通过解析 .、 .. 和符号链接异步地计算规范路径名。仅支持可转换为 UTF8 字符串的路径。    "]}, {"name": "fs.realpathSync(path[, options])", "trans": ["同步的 fs.realpath()"]}, {"name": "fs.realpathSync.native(path[, options])", "trans": ["同步的 fs.realpath.native()"]}, {"name": "fs.rename(old<PERSON><PERSON>, new<PERSON>ath, callback)", "trans": ["异步地把 oldPath 文件重命名为 newPath 提供的路径名。 如果 newPath 已存在，则覆盖它。 "]}, {"name": "fs.renameSync(oldPath, newPath)", "trans": ["同步地把 oldPath 文件重命名为 newPath 提供的路径名。 如果 newPath 已存在，则覆盖它。 "]}, {"name": "fs.rmdir(path[, options], callback)", "trans": ["异步的 rmdir(2)"]}, {"name": "fs.rmdirSync(path[, options])", "trans": ["同步的 rmdir(2)。"]}, {"name": "fs.rm(path[, options], callback)", "trans": ["异步删除文件和目录（以标准POSIX rm实用程序为模型）。"]}, {"name": "fs.rmSync(path[, options])", "trans": ["同步删除文件和目录（以标准POSIX rm实用程序为模型）。"]}, {"name": "fs.stat(path[, options], callback)", "trans": ["异步的 stat(2)"]}, {"name": "fs.statSync(path[, options])", "trans": ["同步的 stat(2)"]}, {"name": "fs.symlink(target, path[, type], callback)", "trans": ["异步的 symlink(2)，它会创建名为 path 的链接，该链接指向 target。"]}, {"name": "fs.symlinkSync(target, path[, type])", "trans": ["同步的 symlink(2)，它会创建名为 path 的链接，该链接指向 target。"]}, {"name": "fs.truncate(path[, len], callback)", "trans": ["异步的 truncate(2)。将文件截断为指定的长度。"]}, {"name": "fs.truncateSync(path[, len])", "trans": ["同步的 truncate(2)。将文件截断为指定的长度。"]}, {"name": "fs.unlink(path, callback)", "trans": ["异步地删除文件或符号链接。 "]}, {"name": "fs.unlinkSync(path)", "trans": ["同步的 unlink(2)。"]}, {"name": "fs.unwatchFile(filename[, listener])", "trans": ["停止监视 filename 的变化。 如果指定了 listener，则仅移除此特定监听器，否则，将移除所有监听器，从而停止监视 filename。"]}, {"name": "fs.utimes(path, atime, mtime, callback)", "trans": ["更改 path 指向的对象的文件系统时间戳。  "]}, {"name": "fs.utimesSync(path, atime, mtime)", "trans": ["更改 path 指向的对象的文件系统时间戳。 "]}, {"name": "fs.watch(filename[, options][, listener])", "trans": ["监视 filename 的更改，其中 filename 是文件或目录。    "]}, {"name": "fs.watchFile(filename[, options], listener)", "trans": ["监视 filename 的更改。 每当访问文件时都会调用 listener 回调。"]}, {"name": "fs.write(fd, buffer[, offset[, length[, position]]], callback)", "trans": ["写入 buffer 到 fd 指定的文件。 如果 buffer 是普通的对象，则它必须具有自身的 toString 函数属性。offset 决定 buffer 中要被写入的部位， length 是整数，指定要写入的字节数。position 指定文件开头的偏移量（数据要被写入的位置）。 如果 typeof position !== 'number'，则数据会被写入当前的位置。 参见 pwrite(2)。"]}, {"name": "fs.write(fd, string[, position[, encoding]], callback)", "trans": ["将 string 写入到 fd 指定的文件。"]}, {"name": "fs.writeFile(file, data[, options], callback)", "trans": ["当 file 是文件名时，则异步地写入数据到文件（如果文件已存在，则覆盖文件）。 data 可以是字符串或 buffer。当 file 是文件描述符时，则其行为类似于直接调用 fs.write()（建议使用）。"]}, {"name": "fs.writeFileSync(file, data[, options])", "trans": ["详见此 API 的异步版本的文档：fs.writeFile()。    "]}, {"name": "fs.writeSync(fd, buffer[, offset[, length[, position]]])", "trans": ["详见此 API 的异步版本的文档：fs.write(fd, buffer...)。"]}, {"name": "fs.writeSync(fd, string[, position[, encoding]])", "trans": ["详见此 API 的异步版本的文档： fs.write(fd, string...)。    "]}, {"name": "fs.writev(fd, buffers[, position], callback)", "trans": ["使用 writev() 将一个 ArrayBufferView 数组写入 fd 指定的文件。"]}, {"name": "fs.writevSync(fd, buffers[, position])", "trans": ["使用 writev() 将一个 ArrayBufferView 数组写入 fd 指定的文件。"]}]