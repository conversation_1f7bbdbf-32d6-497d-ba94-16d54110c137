[{"name": "altzone", "trans": ["返回格林威治西部的夏令时地区的偏移秒数。如果该地区在格林威治东部会返回负值（如西欧，包括英国）。对夏令时启用地区才能使用。"]}, {"name": "asctime()", "trans": ["接受时间元组并返回一个可读的形式为'Tue Dec 11 18:07:14 2008'（2008年12月11日 周二18时07分14秒）的 24 个字符的字符串"]}, {"name": "clock()", "trans": ["用以浮点数计算的秒数返回当前的 CPU 时间。用来衡量不同程序的耗时，比 time.time() 更有用。"]}, {"name": "ctime()", "trans": ["Python time ctime() 函数把一个时间戳（按秒计算的浮点数）转化为time.asctime()的形式。 如果参数未给或者为None的时候，将会默认time.time()为参数。它的作用相当于 asctime(localtime(secs))。"]}, {"name": "gmtime()", "trans": ["接收时间辍（1970 纪元后经过的浮点秒数）并返回格林威治天文时间下的时间元组 t。注：t.tm_isdst 始终为 0"]}, {"name": "localtime()", "trans": ["接收时间辍（1970 纪元后经过的浮点秒数）并返回当地时间下的时间元组 t（t.tm_isdst 可取 0 或 1，取决于当地当时是不是夏令时）。"]}, {"name": "mktime()", "trans": ["接受时间元组并返回时间辍（1970 纪元后经过的浮点秒数）。"]}, {"name": "sleep()", "trans": ["推迟调用线程的运行"]}, {"name": "isocalendar()", "trans": ["isocalendar()返回指定日期的年，第几周，周几这三个值。"]}, {"name": "isoformat()", "trans": ["返回一个字符串，代表ISO 8601格式的日期，YYY-MM-DD。"]}, {"name": "isoweekday()", "trans": ["返回一周的整数"]}, {"name": "strftime()", "trans": ["strftime() 函数接收以时间元组，并返回以可读字符串表示的当地时间，格式由参数format决定"]}, {"name": "timetuple()", "trans": ["timetuple()等同于time.struct_time((d.year, d.month, d.day, 0, 0, 0, d.weekday(), yday, -1))"]}, {"name": "toordinal()", "trans": ["返回该日期的公历序数"]}, {"name": "weekday()", "trans": ["将星期几作为一个整数返回，其中星期一为0，星期日为6"]}, {"name": "astimezone()", "trans": ["返回一个带有新的tzinfo属性tz的日期时间对象，调整日期和时间数据，使结果与自己的UTC时间相同，但以tz的当地时间为准。"]}, {"name": "date()", "trans": ["返回具有相同年份、月份和日期的日期对象。"]}, {"name": "dst()", "trans": ["判断是否是夏令时"]}, {"name": "isocalendar()", "trans": ["返回一个有三个组成部分的命名元组：年、周和工作日"]}, {"name": "isoweekday()", "trans": ["返回一周的整数，其中周一为1，周日为7"]}, {"name": "time()", "trans": ["返回具有相同时、分、秒、微秒和折线的时间对象"]}, {"name": "timetz()", "trans": ["此方法的返回类型是具有相同时，分，秒，微秒，倍数和tzinfo的时间对象。"]}, {"name": "tzname()", "trans": ["包含一对根据情况的不同而不同的字符串，分别是带夏令时的本地时区名称，和不带的。"]}, {"name": "%%", "trans": ["%号本身"]}, {"name": "%a", "trans": ["本地简化星期名称"]}, {"name": "%b", "trans": ["本地简化的月份名称"]}, {"name": "%c", "trans": ["本地相应的日期表示和时间表示"]}, {"name": "%d", "trans": ["月内中的一天（0-31）"]}, {"name": "%H", "trans": ["24小时制小时数（0-23）"]}, {"name": "%I", "trans": ["12小时制小时数（01-12）"]}, {"name": "%j", "trans": ["年内的一天（001-366）"]}, {"name": "%m", "trans": ["月份（01-12）"]}, {"name": "%p", "trans": ["本地A.M.或P.M.的等价符"]}, {"name": "%S", "trans": ["秒（00-59）"]}, {"name": "%U", "trans": ["一年中的星期数（00-53）星期天为星期的开始"]}, {"name": "%w", "trans": ["星期（0-6），星期天为星期的开始"]}, {"name": "%x", "trans": ["本地相应的日期表示"]}, {"name": "%y", "trans": ["两位数的年份表示（00-99）"]}, {"name": "%Z", "trans": ["当前时区的名称"]}]