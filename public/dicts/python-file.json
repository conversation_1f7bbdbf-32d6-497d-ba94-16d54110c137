[{"name": "close()", "trans": ["close() 方法用于关闭一个已打开的文件。关闭后的文件不能再进行读写操作， 否则会触发 ValueError 错误。 close() 方法允许调用多次。当 file 对象，被引用到操作另外一个文件时，Python 会自动关闭之前的 file 对象。 使用 close() 方法关闭文件是一个好的习惯。"]}, {"name": "fileno()", "trans": ["fileno() 方法返回一个整型的文件描述符(file descriptor FD 整型)，可用于底层操作系统的 I/O 操作。"]}, {"name": "flush()", "trans": ["flush() 方法是用来刷新缓冲区的，即将缓冲区中的数据立刻写入文件，同时清空缓冲区，不需要是被动的等待输出缓冲区写入。一般情况下，文件关闭后会自动刷新缓冲区，但有时你需要在关闭前刷新它，这时就可以使用 flush() 方法。"]}, {"name": "isatty()", "trans": ["isatty() 方法检测文件是否连接到一个终端设备，如果是返回 True，否则返回 False。"]}, {"name": "next()", "trans": ["next() 方法在文件使用迭代器时会使用到，在循环中，next()方法会在每次循环中调用，该方法返回文件的下一行，如果到达结尾(EOF)，则触发 StopIteration。"]}, {"name": "read()", "trans": ["read() 方法用于从文件读取指定的字节数，如果未给定或为负则读取所有。"]}, {"name": "readline()", "trans": ["readline() 方法用于从文件读取整行，包括 '\n' 字符。如果指定了一个非负数的参数，则返回指定大小的字节数，包括 '\n' 字符。"]}, {"name": "readlines()", "trans": ["readlines() 方法用于读取所有行(直到结束符 EOF)并返回列表，若给定sizeint>0，返回总和大约为sizeint字节的行, 实际读取值可能比sizhint较大, 因为需要填充缓冲区。如果碰到结束符 EOF 则返回空字符串。"]}, {"name": "seek()", "trans": ["seek() 方法用于移动文件读取指针到指定位置。"]}, {"name": "tell()", "trans": ["tell() 方法返回文件的当前位置，即文件指针当前位置。"]}, {"name": "truncate()", "trans": ["truncate() 方法用于截断文件，如果指定了可选参数 size，则表示截断文件为 size 个字符。 如果没有指定 size，则从当前位置起截断；截断之后 size 后面的所有字符被删除。"]}, {"name": "write()", "trans": ["write() 方法用于向文件中写入指定字符串。在文件关闭前或缓冲区刷新前，字符串内容存储在缓冲区中，这时你在文件中是看不到写入的内容的。"]}, {"name": "writelines()", "trans": ["writelines() 方法用于向文件中写入一序列的字符串。这一序列字符串可以是由迭代对象产生的，如一个字符串列表。 换行需要制定换行符\n。"]}, {"name": "xreadlines()", "trans": ["返回一个生成器，来循环操作文件的每一行。循环使用时和readlines基本一样，但是直接打印就不同"]}, {"name": "closed", "trans": ["如果文件已被关闭返回 True，否则返回 False。"]}, {"name": "encoding()", "trans": [" encoding 指定的编码格式编码字符串。errors参数可以指定不同的错误处理方案。"]}, {"name": "errors", "trans": ["如果该文件无法被打开，会被抛出"]}, {"name": "name", "trans": ["文件名"]}, {"name": "mode", "trans": ["文件打开模式"]}, {"name": "newlines", "trans": ["表示文件所采用的分隔符"]}, {"name": "softspace", "trans": ["文件末尾强制加空格标志,为0表示在输出一数据后，要再加上一个空格符，为1表示不加，这个属性一般用不到"]}]