[{"name": "x in s", "trans": ["判断键是否存在于字典中，如果键在字典 dict 里返回 true，否则返回 false"]}, {"name": "add()", "trans": ["用于给集合添加元素，如果添加的元素在集合中已存在，则不执行任何操作"]}, {"name": "clear()", "trans": ["用于移除集合中的所有元素"]}, {"name": "copy()", "trans": ["用于拷贝一个集合"]}, {"name": "difference_update()", "trans": ["用于移除两个集合中都存在的元素，没有返回值"]}, {"name": "difference()", "trans": ["返回一个移除相同元素的新集合"]}, {"name": "discard()", "trans": ["用于移除指定的集合元素"]}, {"name": "intersection_update()", "trans": ["用于获取两个或更多集合中都重叠的元素，原始的集合上移除不重叠的元素，即计算交集"]}, {"name": "intersection()", "trans": ["返回两个或更多集合中都包含的元素，即交集，返回一个新的集合"]}, {"name": "len()", "trans": ["返回对象（字符、列表、元组等）长度或项目个数"]}, {"name": "pop()", "trans": ["随机移除一个元素"]}, {"name": "remove()", "trans": ["移除集合中的指定元素"]}, {"name": "isdisjoint()", "trans": ["判断两个集合是否包含相同的元素，如果没有返回 True，否则返回 False。"]}, {"name": "issubset()", "trans": ["判断集合的所有元素是否都包含在指定集合中，如果是则返回 True，否则返回 False"]}, {"name": "issuperset()", "trans": ["判断指定集合的所有元素是否都包含在原始的集合中，如果是则返回 True，否则返回 False"]}, {"name": "symmetric_difference_update()", "trans": ["移除当前集合中在另外一个指定集合相同的元素，并将另外一个指定集合中不同的元素插入到当前集合中"]}, {"name": "symmetric_difference()", "trans": ["返回两个集合中不重复的元素集合，即会移除两个集合中都存在的元素。"]}, {"name": "union()", "trans": ["返回两个集合的并集，即包含了所有集合的元素，重复的元素只会出现一次"]}, {"name": "update()", "trans": ["修改当前集合，可以添加新的元素或集合到当前集合中，如果添加的元素在集合中已存在，则该元素只会出现一次，重复的会忽略"]}, {"name": "fromkeys()", "trans": ["创建一个新字典，以序列 seq 中元素做字典的键，value 为字典所有键对应的初始值"]}, {"name": "get()", "trans": ["返回指定键的值"]}, {"name": "has_key()", "trans": ["判断键是否存在于字典中，如果键在字典dict里返回true，否则返回false"]}, {"name": "items()", "trans": ["以列表返回可遍历的(键, 值) 元组数组"]}, {"name": "iteritems()", "trans": ["将一个字典以列表的形式返回，因为字典是无序的，所以返回的列表也是无序的"]}, {"name": "iterkeys()", "trans": ["返回一个迭代器"]}, {"name": "keys()", "trans": ["返回一个视图对象"]}, {"name": "popitem()", "trans": ["返回并删除字典中的最后一对键和值"]}, {"name": "setdefault()", "trans": ["如果键不存在于字典中，将会添加键并将值设为默认值"]}, {"name": "values()", "trans": ["返回一个视图对象"]}]