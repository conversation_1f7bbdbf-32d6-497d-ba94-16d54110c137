[{"name": "String.fromCharCode()", "trans": ["静态 String.fromCharCode() 方法返回由指定的UTF-16代码单元序列创建的字符串。"]}, {"name": "String.fromCodePoint()", "trans": ["String.fromCodePoint() 静态方法返回使用指定的代码点序列创建的字符串。"]}, {"name": "charAt()", "trans": ["charAt() 方法从一个字符串中返回指定的字符。"]}, {"name": "charCodeAt()", "trans": ["charCodeAt() 方法返回0到65535之间的整数，表示给定索引处的UTF-16代码单元 (在 Unicode 编码单元表示一个单一的 UTF-16 编码单元的情况下，UTF-16 编码单元匹配 Unicode 编码单元。但在——例如 Unicode 编码单元 > 0x10000 的这种——不能被一个 UTF-16 编码单元单独表示的情况下，只能匹配 Unicode 代理对的第一个编码单元) 。如果你想要整个代码点的值，使用 codePointAt()。"]}, {"name": "codePointAt()", "trans": ["codePointAt() 方法返回 一个 Unicode 编码点值的非负整数。"]}, {"name": "concat()", "trans": ["concat() 方法将一个或多个字符串与原字符串连接合并，形成一个新的字符串并返回。"]}, {"name": "endsWith()", "trans": ["endsWith()方法用来判断当前字符串是否是以另外一个给定的子字符串“结尾”的，根据判断结果返回 true 或 false。"]}, {"name": "includes()", "trans": ["includes() 方法用于判断一个字符串是否包含在另一个字符串中，根据情况返回 true 或 false。"]}, {"name": "indexOf()", "trans": ["indexOf() 方法返回调用它的 String 对象中第一次出现的指定值的索引，从 fromIndex 处进行搜索。如果未找到该值，则返回 -1。"]}, {"name": "lastIndexOf()", "trans": ["lastIndexOf() 方法返回指定值在调用该方法的字符串中最后出现的位置，如果没找到则返回 -1。length为需要检索字符串的长度，默认值为str.length。"]}, {"name": "localeCompare()", "trans": ["localeCompare() 方法返回一个数字来指示一个参考字符串是否在排序顺序前面或之后或与给定字符串相同。"]}, {"name": "match()", "trans": ["match() 方法检索返回一个字符串匹配正则表达式的的结果。"]}, {"name": "matchAll()", "trans": ["matchAll() 方法返回一个包含所有匹配正则表达式及分组捕获结果的迭代器。"]}, {"name": "normalize()", "trans": ["normalize() 方法会按照指定的一种 Unicode 正规形式将当前字符串正规化."]}, {"name": "padEnd()", "trans": ["padEnd()  方法会用一个字符串填充当前字符串（如果需要的话则重复填充），返回填充后达到指定长度的字符串。从当前字符串的末尾（右侧）开始填充。"]}, {"name": "padStart()", "trans": ["padStart() 方法用另一个字符串填充当前字符串(重复，如果需要的话)，以便产生的字符串达到给定的长度。填充从当前字符串的开始(左侧)应用的。"]}, {"name": "repeat()", "trans": ["repeat() 构造并返回一个新字符串，该字符串包含被连接在一起的指定数量的字符串的副本。"]}, {"name": "replace()", "trans": ["replace() 方法返回一个由替换值（replacement）替换一些或所有匹配的模式（pattern）后的新字符串。模式可以是一个字符串或者一个正则表达式，替换值可以是一个字符串或者一个每次匹配都要调用的回调函数。"]}, {"name": "search()", "trans": ["search() 方法执行正则表达式和 String 对象之间的一个搜索匹配。"]}, {"name": "slice()", "trans": ["slice() 方法提取某个字符串的一部分，并返回一个新的字符串，且不会改动原字符串。"]}, {"name": "split()", "trans": ["split() 方法使用指定的分隔符字符串将一个String对象分割成字符串数组，以将字符串分隔为子字符串，以确定每个拆分的位置。"]}, {"name": "startsWith()", "trans": ["startsWith() 方法用来判断当前字符串是否以另外一个给定的子字符串开头，并根据判断结果返回 true 或 false。"]}, {"name": "substring()", "trans": ["substring() 方法返回一个字符串在开始索引到结束索引之间的一个子集, 或从开始索引直到字符串的末尾的一个子集。"]}, {"name": "toLocaleLowerCase()", "trans": ["toLocaleLowerCase()方法根据任何特定于语言环境的案例映射，返回调用字符串值转换为小写的值。"]}, {"name": "toLocaleUpperCase()", "trans": ["toLocaleUpperCase() 使用本地化（locale-specific）的大小写映射规则将输入的字符串转化成大写形式并返回结果字符串。"]}, {"name": "toLowerCase()", "trans": ["toLowerCase() 会将调用该方法的字符串值转为小写形式，并返回。"]}, {"name": "toString()", "trans": ["toString() 方法返回指定对象的字符串形式。"]}, {"name": "toUpperCase()", "trans": ["toUpperCase() 将调用该方法的字符串值转换为大写形式，并返回。"]}, {"name": "trim()", "trans": ["trim() 方法会从一个字符串的两端删除空白字符。在这个上下文中的空白字符是所有的空白字符 (space, tab, no-break space 等) 以及所有行终止符字符（如 LF，CR）。"]}, {"name": "trimRight()", "trans": ["trimRight() 方法从一个字符串的右端移除空白字符。"]}, {"name": "trimLeft()", "trans": ["一个新字符串，表示从其开头（左端）剥离空格的调用字符串。"]}, {"name": "String.raw()", "trans": ["String.raw() 是一个模板字符串的标签函数，它的作用类似于 Python 中的字符串前缀 r 和 C# 中的字符串前缀 @（还是有点区别的，详见隔壁 Chromium 那边的这个 issue），是用来获取一个模板字符串的原始字符串的，比如说，占位符（例如 ${foo}）会被处理为它所代表的其他字符串，而转义字符（例如 \\n）不会。"]}]