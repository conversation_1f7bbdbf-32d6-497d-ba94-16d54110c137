[{"name": "eval()", "trans": ["eval() 函数会将传入的字符串当做 JavaScript 代码进行执行。"]}, {"name": "isFinite()", "trans": ["该全局 isFinite() 函数用来判断被传入的参数值是否为一个有限数值（finite number）。在必要情况下，参数会首先转为一个数值。"]}, {"name": "isNaN()", "trans": ["isNaN() 函数用来确定一个值是否为NaN 。注：isNaN函数内包含一些非常有趣的规则；你也可以使用 ECMAScript 2015 中定义的 Number.isNaN() 来判断。"]}, {"name": "parseFloat()", "trans": ["parseFloat() 函数解析一个参数（必要时先转换为字符串）并返回一个浮点数。"]}, {"name": "parseInt()", "trans": ["parseInt(string, radix)   将一个字符串 string 转换为 radix 进制的整数， radix 为介于2-36之间的数。"]}, {"name": "decodeURI()", "trans": ["decodeURI() 函数解码一个由encodeURI 先前创建的统一资源标识符（URI）或类似的例程。"]}, {"name": "decodeURIComponent()", "trans": ["decodeURIComponent() 方法用于解码由 encodeURIComponent 方法或者其它类似方法编码的部分统一资源标识符（URI）。"]}, {"name": "encodeURI()", "trans": ["encodeURI()  函数通过将特定字符的每个实例替换为一个、两个、三或四转义序列来对统一资源标识符 (URI) 进行编码 (该字符的 UTF-8 编码仅为四转义序列)由两个 \"代理\" 字符组成)。"]}, {"name": "encodeURIComponent()", "trans": ["encodeURIComponent()是对统一资源标识符（URI）的组成部分进行编码的方法。它使用一到四个转义序列来表示字符串中的每个字符的UTF-8编码（只有由两个Unicode代理区字符组成的字符才用四个转义字符编码）。"]}]