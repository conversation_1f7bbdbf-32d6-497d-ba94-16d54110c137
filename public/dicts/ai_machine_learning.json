[{"name": "Loss Function", "trans": ["损失函数"]}, {"name": "Accept-Reject Sampling Method", "trans": ["接受-拒绝抽样法/接受-拒绝采样法"]}, {"name": "Accumulated Error Backpropagation", "trans": ["累积误差反向传播"]}, {"name": "Accuracy", "trans": ["准确率"]}, {"name": "Acquisition Function", "trans": ["采集函数"]}, {"name": "Action", "trans": ["动作"]}, {"name": "Activation Function", "trans": ["激活函数"]}, {"name": "Active Learning", "trans": ["主动学习"]}, {"name": "Adaptive Bitrate Algorithm", "trans": ["自适应比特率算法"]}, {"name": "Adaptive Boosting", "trans": ["AdaBoost"]}, {"name": "Adaptive Gradient Algorithm", "trans": ["AdaGrad"]}, {"name": "Adaptive Moment Estimation Algorithm", "trans": ["<PERSON>算法"]}, {"name": "Adaptive Resonance Theory", "trans": ["自适应谐振理论"]}, {"name": "Additive Model", "trans": ["加性模型"]}, {"name": "Affinity Matrix", "trans": ["亲和矩阵"]}, {"name": "Agent", "trans": ["智能体"]}, {"name": "Algorithm", "trans": ["算法"]}, {"name": "Alpha-Beta Pruning", "trans": ["α-β修剪法"]}, {"name": "Anomaly Detection", "trans": ["异常检测"]}, {"name": "Approximate Inference", "trans": ["近似推断"]}, {"name": "Area Under ROC Curve", "trans": ["AUC（ROC曲线下方面积，度量分类模型好坏的标准）"]}, {"name": "Artificial Intelligence", "trans": ["人工智能"]}, {"name": "Artificial Neural Network", "trans": ["人工神经网络"]}, {"name": "Artificial Neuron", "trans": ["人工神经元"]}, {"name": "Attention", "trans": ["注意力"]}, {"name": "Attention Mechanism", "trans": ["注意力机制"]}, {"name": "Attribute", "trans": ["属性"]}, {"name": "Attribute Space", "trans": ["属性空间"]}, {"name": "Autoencoder", "trans": ["自编码器"]}, {"name": "Automatic Differentiation", "trans": ["自动微分"]}, {"name": "Autoregressive Model", "trans": ["自回归模型"]}, {"name": "Back Propagation", "trans": ["反向传播"]}, {"name": "Back Propagation Algorithm", "trans": ["反向传播算法"]}, {"name": "Back Propagation Through Time", "trans": ["随时间反向传播"]}, {"name": "Backward Induction", "trans": ["反向归纳"]}, {"name": "Backward Search", "trans": ["反向搜索"]}, {"name": "Bag of Words", "trans": ["词袋"]}, {"name": "Bandit", "trans": ["赌博机/老虎机"]}, {"name": "Base Learner", "trans": ["基学习器"]}, {"name": "Base Learning Algorithm", "trans": ["基学习算法"]}, {"name": "Baseline", "trans": ["基准"]}, {"name": "<PERSON><PERSON>", "trans": ["批量"]}, {"name": "Batch Normalization", "trans": ["批量规范化"]}, {"name": "Bayes Decision Rule", "trans": ["贝叶斯决策准则"]}, {"name": "Bayes Model Averaging", "trans": ["贝叶斯模型平均"]}, {"name": "Bayes Optimal Classifier", "trans": ["贝叶斯最优分类器"]}, {"name": "<PERSON><PERSON>' Theorem", "trans": ["贝叶斯定理"]}, {"name": "Bayesian Decision Theory", "trans": ["贝叶斯决策理论"]}, {"name": "Bayesian Inference", "trans": ["贝叶斯推断"]}, {"name": "Bayesian Learning", "trans": ["贝叶斯学习"]}, {"name": "Bayesian Network", "trans": ["贝叶斯网/贝叶斯网络"]}, {"name": "Bayesian Optimization", "trans": ["贝叶斯优化"]}, {"name": "<PERSON>am <PERSON>", "trans": ["束搜索"]}, {"name": "Benchmark", "trans": ["基准"]}, {"name": "Belief Network", "trans": ["信念网/信念网络"]}, {"name": "Belief Propagation", "trans": ["信念传播"]}, {"name": "Bellman Equation", "trans": ["贝尔曼方程"]}, {"name": "Bernoulli Distribution", "trans": ["伯努利分布"]}, {"name": "Beta Distribution", "trans": ["贝塔分布"]}, {"name": "Between-Class Scatter Matrix", "trans": ["类间散度矩阵"]}, {"name": "BFGS", "trans": ["BFGS"]}, {"name": "Bias", "trans": ["偏差/偏置"]}, {"name": "Bias In Affine Function", "trans": ["偏置"]}, {"name": "Bias In Statistics", "trans": ["偏差"]}, {"name": "<PERSON><PERSON>", "trans": ["偏置偏移"]}, {"name": "Bias-<PERSON><PERSON><PERSON>", "trans": ["偏差 - 方差分解"]}, {"name": "Bias-<PERSON><PERSON><PERSON> Dilemma", "trans": ["偏差 - 方差困境"]}, {"name": "Bidirectional Recurrent Neural Network", "trans": ["双向循环神经网络"]}, {"name": "Bigram", "trans": ["二元语法"]}, {"name": "Bilingual Evaluation Understudy", "trans": ["BLEU"]}, {"name": "Binary Classification", "trans": ["二分类"]}, {"name": "Binomial Distribution", "trans": ["二项分布"]}, {"name": "Binomial Test", "trans": ["二项检验"]}, {"name": "Boltzmann Distribution", "trans": ["玻尔兹曼分布"]}, {"name": "Boltzmann Machine", "trans": ["玻尔兹曼机"]}, {"name": "Boosting", "trans": ["Boosting（一种模型训练加速方式）"]}, {"name": "Bootstrap Aggregating", "trans": ["Bagging"]}, {"name": "Bootstrap Sampling", "trans": ["自助采样法"]}, {"name": "Bootstrapping", "trans": ["自助法/自举法"]}, {"name": "Break-Event Point", "trans": ["平衡点"]}, {"name": "Bucketing", "trans": ["分桶"]}, {"name": "Calculus of Variations", "trans": ["变分法"]}, {"name": "Cascade-Correlation", "trans": ["级联相关"]}, {"name": "Catastrophic Forgetting", "trans": ["灾难性遗忘"]}, {"name": "Categorical Distribution", "trans": ["类别分布"]}, {"name": "Cell", "trans": ["单元"]}, {"name": "Chain Rule", "trans": ["链式法则"]}, {"name": "Chebyshev Distance", "trans": ["切比雪夫距离"]}, {"name": "Class", "trans": ["类别"]}, {"name": "Class-Imbalance", "trans": ["类别不平衡"]}, {"name": "Classification", "trans": ["分类"]}, {"name": "Classification And Regression Tree", "trans": ["分类与回归树"]}, {"name": "Classifier", "trans": ["分类器"]}, {"name": "Clique", "trans": ["团"]}, {"name": "Cluster", "trans": ["簇"]}, {"name": "Cluster Assumption", "trans": ["聚类假设"]}, {"name": "Clustering", "trans": ["聚类"]}, {"name": "Clustering Ensemble", "trans": ["聚类集成"]}, {"name": "Co-Training", "trans": ["协同训练"]}, {"name": "Coding Matrix", "trans": ["编码矩阵"]}, {"name": "Collaborative Filtering", "trans": ["协同过滤"]}, {"name": "Competitive Learning", "trans": ["竞争型学习"]}, {"name": "Comprehensibility", "trans": ["可解释性"]}, {"name": "Computation Graph", "trans": ["计算图"]}, {"name": "Computational Learning Theory", "trans": ["计算学习理论"]}, {"name": "Conditional Entropy", "trans": ["条件熵"]}, {"name": "Conditional Probability", "trans": ["条件概率"]}, {"name": "Conditional Probability Distribution", "trans": ["条件概率分布"]}, {"name": "Conditional Random Field", "trans": ["条件随机场"]}, {"name": "Conditional Risk", "trans": ["条件风险"]}, {"name": "Confidence", "trans": ["置信度"]}, {"name": "Confusion Matrix", "trans": ["混淆矩阵"]}, {"name": "Conjugate Distribution", "trans": ["共轭分布"]}, {"name": "Connection Weight", "trans": ["连接权"]}, {"name": "Connectionism", "trans": ["连接主义"]}, {"name": "Consistency", "trans": ["一致性"]}, {"name": "Constrained Optimization", "trans": ["约束优化"]}, {"name": "Context Variable", "trans": ["上下文变量"]}, {"name": "Context Vector", "trans": ["上下文向量"]}, {"name": "Context Window", "trans": ["上下文窗口"]}, {"name": "Context Word", "trans": ["上下文词"]}, {"name": "Contextual Bandit", "trans": ["上下文赌博机/上下文老虎机"]}, {"name": "Contingency Table", "trans": ["列联表"]}, {"name": "Continuous Attribute", "trans": ["连续属性"]}, {"name": "Contrastive Divergence", "trans": ["对比散度"]}, {"name": "Convergence", "trans": ["收敛"]}, {"name": "Convex Optimization", "trans": ["凸优化"]}, {"name": "Convex Quadratic Programming", "trans": ["凸二次规划"]}, {"name": "Convolution", "trans": ["卷积"]}, {"name": "Convolutional Kernel", "trans": ["卷积核"]}, {"name": "Convolutional Neural Network", "trans": ["卷积神经网络"]}, {"name": "Coordinate Descent", "trans": ["坐标下降"]}, {"name": "Corpus", "trans": ["语料库"]}, {"name": "Correlation Coefficient", "trans": ["相关系数"]}, {"name": "Cosine Similarity", "trans": ["余弦相似度"]}, {"name": "Cost", "trans": ["代价"]}, {"name": "Cost Curve", "trans": ["代价曲线"]}, {"name": "Cost Function", "trans": ["代价函数"]}, {"name": "Cost Matrix", "trans": ["代价矩阵"]}, {"name": "Cost-Sensitive", "trans": ["代价敏感"]}, {"name": "Covariance", "trans": ["协方差"]}, {"name": "Covariance Matrix", "trans": ["协方差矩阵"]}, {"name": "Critical Point", "trans": ["临界点"]}, {"name": "Cross Entropy", "trans": ["交叉熵"]}, {"name": "Cross Validation", "trans": ["交叉验证"]}, {"name": "Curse of Dimensionality", "trans": ["维数灾难"]}, {"name": "Cutting Plane Algorithm", "trans": ["割平面法"]}, {"name": "Data Mining", "trans": ["数据挖掘"]}, {"name": "Data Set", "trans": ["数据集"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trans": ["DFP"]}, {"name": "Decision Boundary", "trans": ["决策边界"]}, {"name": "Decision Function", "trans": ["决策函数"]}, {"name": "Decision Stump", "trans": ["决策树桩"]}, {"name": "Decision Tree", "trans": ["决策树"]}, {"name": "Decoder", "trans": ["解码器"]}, {"name": "Decoding", "trans": ["解码"]}, {"name": "Deconvolution", "trans": ["反卷积"]}, {"name": "Deconvolutional Network", "trans": ["反卷积网络"]}, {"name": "Deduction", "trans": ["演绎"]}, {"name": "Deep Belief Network", "trans": ["深度信念网络"]}, {"name": "<PERSON> Machine", "trans": ["深度玻尔兹曼机"]}, {"name": "Deep Convolutional Generative Adversarial Network", "trans": ["深度卷积生成对抗网络"]}, {"name": "Deep Learning", "trans": ["深度学习"]}, {"name": "Deep Neural Network", "trans": ["深度神经网络"]}, {"name": "Deep Q-Network", "trans": ["深度Q网络"]}, {"name": "Delta-Bar-Delta", "trans": ["Delta-Bar-Delta"]}, {"name": "Denoising", "trans": ["去噪"]}, {"name": "Denoising Autoencoder", "trans": ["去噪自编码器"]}, {"name": "Denoising Score Matching", "trans": ["去躁分数匹配"]}, {"name": "Density Estimation", "trans": ["密度估计"]}, {"name": "Density-Based Clustering", "trans": ["密度聚类"]}, {"name": "Derivative", "trans": ["导数"]}, {"name": "Determinant", "trans": ["行列式"]}, {"name": "Diagonal Matrix", "trans": ["对角矩阵"]}, {"name": "Dictionary Learning", "trans": ["字典学习"]}, {"name": "Dimension Reduction", "trans": ["降维"]}, {"name": "Directed Edge", "trans": ["有向边"]}, {"name": "Directed Graphical Model", "trans": ["有向图模型"]}, {"name": "Directed Separation", "trans": ["有向分离"]}, {"name": "Dirichlet Distribution", "trans": ["狄利克雷分布"]}, {"name": "Discriminative Model", "trans": ["判别式模型"]}, {"name": "Discriminator", "trans": ["判别器"]}, {"name": "Discriminator Network", "trans": ["判别网络"]}, {"name": "Distance Measure", "trans": ["距离度量"]}, {"name": "Distance Metric Learning", "trans": ["距离度量学习"]}, {"name": "Distributed Representation", "trans": ["分布式表示"]}, {"name": "Diverge", "trans": ["发散"]}, {"name": "Divergence", "trans": ["散度"]}, {"name": "Diversity", "trans": ["多样性"]}, {"name": "Diversity Measure", "trans": ["多样性度量/差异性度量"]}, {"name": "Domain Adaptation", "trans": ["领域自适应"]}, {"name": "Dominant <PERSON>igen<PERSON>ue", "trans": ["主特征值"]}, {"name": "Dominant Strategy", "trans": ["占优策略"]}, {"name": "Down Sampling", "trans": ["下采样"]}, {"name": "Dropout", "trans": ["暂退法"]}, {"name": "Dropout Boosting", "trans": ["暂退Boosting"]}, {"name": "Dropout Method", "trans": ["暂退法"]}, {"name": "Dual Problem", "trans": ["对偶问题"]}, {"name": "Dummy <PERSON>", "trans": ["哑结点"]}, {"name": "Dynamic Bayesian Network", "trans": ["动态贝叶斯网络"]}, {"name": "Dynamic Programming", "trans": ["动态规划"]}, {"name": "Early Stopping", "trans": ["早停"]}, {"name": "Eigendecomposition", "trans": ["特征分解"]}, {"name": "Eigenvalue", "trans": ["特征值"]}, {"name": "Element-Wise Product", "trans": ["逐元素积"]}, {"name": "Embedding", "trans": ["嵌入"]}, {"name": "Empirical Conditional Entropy", "trans": ["经验条件熵"]}, {"name": "Empirical Distribution", "trans": ["经验分布"]}, {"name": "Empirical Entropy", "trans": ["经验熵"]}, {"name": "Empirical Error", "trans": ["经验误差"]}, {"name": "Empirical Risk", "trans": ["经验风险"]}, {"name": "Empirical Risk Minimization", "trans": ["经验风险最小化"]}, {"name": "Encoder", "trans": ["编码器"]}, {"name": "Encoding", "trans": ["编码"]}, {"name": "End-To-End", "trans": ["端到端"]}, {"name": "Energy Function", "trans": ["能量函数"]}, {"name": "Energy-Based Model", "trans": ["基于能量的模型"]}, {"name": "Ensemble Learning", "trans": ["集成学习"]}, {"name": "Ensemble Pruning", "trans": ["集成修剪"]}, {"name": "Entropy", "trans": ["熵"]}, {"name": "Episode", "trans": ["回合"]}, {"name": "Epoch", "trans": ["轮"]}, {"name": "Error", "trans": ["误差"]}, {"name": "Error Backpropagation Algorithm", "trans": ["误差反向传播算法"]}, {"name": "Error Backpropagation", "trans": ["误差反向传播"]}, {"name": "Error Correcting Output Codes", "trans": ["纠错输出编码"]}, {"name": "Error Rate", "trans": ["错误率"]}, {"name": "Error-Ambiguity Decomposition", "trans": ["误差－分歧分解"]}, {"name": "Estimator", "trans": ["估计/估计量"]}, {"name": "Euclidean Distance", "trans": ["欧氏距离"]}, {"name": "Evidence", "trans": ["证据"]}, {"name": "Evidence Lower Bound", "trans": ["证据下界"]}, {"name": "Exact Inference", "trans": ["精确推断"]}, {"name": "Example", "trans": ["样例"]}, {"name": "Expectation", "trans": ["期望"]}, {"name": "Expectation Maximization", "trans": ["期望最大化"]}, {"name": "Expected Loss", "trans": ["期望损失"]}, {"name": "Expert System", "trans": ["专家系统"]}, {"name": "Exploding Gradient", "trans": ["梯度爆炸"]}, {"name": "Exponential Loss Function", "trans": ["指数损失函数"]}, {"name": "Factor", "trans": ["因子"]}, {"name": "Factorization", "trans": ["因子分解"]}, {"name": "Feature", "trans": ["特征"]}, {"name": "Feature Engineering", "trans": ["特征工程"]}, {"name": "Feature Map", "trans": ["特征图"]}, {"name": "Feature Selection", "trans": ["特征选择"]}, {"name": "Feature Vector", "trans": ["特征向量"]}, {"name": "Featured Learning", "trans": ["特征学习"]}, {"name": "Feedforward", "trans": ["前馈"]}, {"name": "Feedforward Neural Network", "trans": ["前馈神经网络"]}, {"name": "Few-Shot Learning", "trans": ["少试学习"]}, {"name": "Filter", "trans": ["滤波器"]}, {"name": "Fine-Tuning", "trans": ["微调"]}, {"name": "Fluctuation", "trans": ["振荡"]}, {"name": "Forget Gate", "trans": ["遗忘门"]}, {"name": "Forward Propagation", "trans": ["前向传播/正向传播"]}, {"name": "Forward Stagewise Algorithm", "trans": ["前向分步算法"]}, {"name": "Fractionally Strided Convolution", "trans": ["微步卷积"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["Frobenius 范数"]}, {"name": "Full Padding", "trans": ["全填充"]}, {"name": "Functional", "trans": ["泛函"]}, {"name": "Functional Neuron", "trans": ["功能神经元"]}, {"name": "Gated Recurrent Unit", "trans": ["门控循环单元"]}, {"name": "Gated RNN", "trans": ["门控RNN"]}, {"name": "Gaussian Distribution", "trans": ["高斯分布"]}, {"name": "G<PERSON><PERSON>", "trans": ["高斯核"]}, {"name": "Gaussian Kernel Function", "trans": ["高斯核函数"]}, {"name": "Gaussian Mixture Model", "trans": ["高斯混合模型"]}, {"name": "Gaussian Process", "trans": ["高斯过程"]}, {"name": "Generalization Ability", "trans": ["泛化能力"]}, {"name": "Generalization Error", "trans": ["泛化误差"]}, {"name": "Generalization Error Bound", "trans": ["泛化误差上界"]}, {"name": "Generalize", "trans": ["泛化"]}, {"name": "Generalized Lagrange Function", "trans": ["广义拉格朗日函数"]}, {"name": "Generalized Linear Model", "trans": ["广义线性模型"]}, {"name": "Generalized Rayleigh Quotient", "trans": ["广义瑞利商"]}, {"name": "Generative Adversarial Network", "trans": ["生成对抗网络"]}, {"name": "Generative Model", "trans": ["生成式模型"]}, {"name": "Generator", "trans": ["生成器"]}, {"name": "Generator Network", "trans": ["生成器网络"]}, {"name": "Genetic Algorithm", "trans": ["遗传算法"]}, {"name": "Gibbs Distribution", "trans": ["吉布斯分布"]}, {"name": "<PERSON>", "trans": ["吉布斯采样/吉布斯抽样"]}, {"name": "Gini Index", "trans": ["基尼指数"]}, {"name": "Global Markov Property", "trans": ["全局马尔可夫性"]}, {"name": "Global Minimum", "trans": ["全局最小"]}, {"name": "Gradient", "trans": ["梯度"]}, {"name": "Grad<PERSON> Clipping", "trans": ["梯度截断"]}, {"name": "<PERSON><PERSON><PERSON> Descent", "trans": ["梯度下降"]}, {"name": "Gradient Descent Method", "trans": ["梯度下降法"]}, {"name": "Gradient Exploding Problem", "trans": ["梯度爆炸问题"]}, {"name": "Gram Matrix", "trans": ["Gram 矩阵"]}, {"name": "Graph Convolutional Network", "trans": ["图卷积神经网络/图卷积网络"]}, {"name": "Graph Neural Network", "trans": ["图神经网络"]}, {"name": "Graphical Model", "trans": ["图模型"]}, {"name": "Grid Search", "trans": ["网格搜索"]}, {"name": "Ground Truth", "trans": ["真实值"]}, {"name": "Hadamard Product", "trans": ["<PERSON><PERSON><PERSON>积"]}, {"name": "Hamming Distance", "trans": ["汉明距离"]}, {"name": "<PERSON>", "trans": ["硬间隔"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["赫布法则"]}, {"name": "Hidden Layer", "trans": ["隐藏层"]}, {"name": "<PERSON> Model", "trans": ["隐马尔可夫模型"]}, {"name": "Hidden Variable", "trans": ["隐变量"]}, {"name": "Hierarchical Clustering", "trans": ["层次聚类"]}, {"name": "<PERSON>lbert Space", "trans": ["希尔伯特空间"]}, {"name": "Hinge Loss Function", "trans": ["合页损失函数/Hinge损失函数"]}, {"name": "Hold-Out", "trans": ["留出法"]}, {"name": "Hyperparameter", "trans": ["超参数"]}, {"name": "Hyperparameter Optimization", "trans": ["超参数优化"]}, {"name": "Hypothesis", "trans": ["假设"]}, {"name": "Hypothesis Space", "trans": ["假设空间"]}, {"name": "Hypothesis Test", "trans": ["假设检验"]}, {"name": "Identity Matrix", "trans": ["单位矩阵"]}, {"name": "Imitation Learning", "trans": ["模仿学习"]}, {"name": "Importance Sampling", "trans": ["重要性采样"]}, {"name": "Improved Iterative Scaling", "trans": ["改进的迭代尺度法"]}, {"name": "Incremental Learning", "trans": ["增量学习"]}, {"name": "Independent and Identically Distributed", "trans": ["独立同分布"]}, {"name": "Indicator Function", "trans": ["指示函数"]}, {"name": "Individual Learner", "trans": ["个体学习器"]}, {"name": "Induction", "trans": ["归纳"]}, {"name": "Inductive Bias", "trans": ["归纳偏好"]}, {"name": "Inductive Learning", "trans": ["归纳学习"]}, {"name": "Inductive Logic Programming", "trans": ["归纳逻辑程序设计"]}, {"name": "Inference", "trans": ["推断"]}, {"name": "Information Entropy", "trans": ["信息熵"]}, {"name": "Information Gain", "trans": ["信息增益"]}, {"name": "Inner Product", "trans": ["内积"]}, {"name": "Instance", "trans": ["示例"]}, {"name": "Internal Covariate Shift", "trans": ["内部协变量偏移"]}, {"name": "Inverse Matrix", "trans": ["逆矩阵"]}, {"name": "Inverse Resolution", "trans": ["逆归结"]}, {"name": "Isometric Mapping", "trans": ["等度量映射"]}, {"name": "Jacobian Matrix", "trans": ["雅可比矩阵"]}, {"name": "Jensen Inequality", "trans": ["Jensen不等式"]}, {"name": "Joint Probability Distribution", "trans": ["联合概率分布"]}, {"name": "K-Armed Bandit Problem", "trans": ["k-摇臂老虎机"]}, {"name": "K-Fold Cross Validation", "trans": ["k 折交叉验证"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON> Condition", "trans": ["KKT条件"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["<PERSON><PERSON><PERSON>"]}, {"name": "Kernel Function", "trans": ["核函数"]}, {"name": "Kernel Method", "trans": ["核方法"]}, {"name": "Kernel Trick", "trans": ["核技巧"]}, {"name": "Kernelized Linear Discriminant Analysis", "trans": ["核线性判别分析"]}, {"name": "KL Divergence", "trans": ["KL散度"]}, {"name": "L-BFGS", "trans": ["L-BFGS"]}, {"name": "Label", "trans": ["标签/标记"]}, {"name": "Label Space", "trans": ["标记空间"]}, {"name": "Lagrange Duality", "trans": ["拉格朗日对偶性"]}, {"name": "Lagrange Multiplier", "trans": ["拉格朗日乘子"]}, {"name": "Language Model", "trans": ["语言模型"]}, {"name": "<PERSON><PERSON>oth<PERSON>", "trans": ["拉普拉斯平滑"]}, {"name": "Laplacian Correction", "trans": ["拉普拉斯修正"]}, {"name": "Latent Dirichlet Allocation", "trans": ["潜在狄利克雷分配"]}, {"name": "Latent Semantic Analysis", "trans": ["潜在语义分析"]}, {"name": "Latent Variable", "trans": ["潜变量/隐变量"]}, {"name": "Law of Large Numbers", "trans": ["大数定律"]}, {"name": "Layer Normalization", "trans": ["层规范化"]}, {"name": "Lazy Learning", "trans": ["懒惰学习"]}, {"name": "<PERSON><PERSON>", "trans": ["泄漏修正线性单元/泄漏整流线性单元"]}, {"name": "<PERSON><PERSON>", "trans": ["学习器"]}, {"name": "Learning", "trans": ["学习"]}, {"name": "Learning By Analogy", "trans": ["类比学习"]}, {"name": "Learning Rate", "trans": ["学习率"]}, {"name": "Learning Vector Quantization", "trans": ["学习向量量化"]}, {"name": "Least Square Method", "trans": ["最小二乘法"]}, {"name": "Least Squares Regression Tree", "trans": ["最小二乘回归树"]}, {"name": "Left Singular Vector", "trans": ["左奇异向量"]}, {"name": "Likelihood", "trans": ["似然"]}, {"name": "Linear Chain Conditional Random Field", "trans": ["线性链条件随机场"]}, {"name": "Linear Classification Model", "trans": ["线性分类模型"]}, {"name": "Linear Classifier", "trans": ["线性分类器"]}, {"name": "Linear Dependence", "trans": ["线性相关"]}, {"name": "Linear Discriminant Analysis", "trans": ["线性判别分析"]}, {"name": "Linear Model", "trans": ["线性模型"]}, {"name": "Linear Regression", "trans": ["线性回归"]}, {"name": "Link Function", "trans": ["联系函数"]}, {"name": "Local Markov Property", "trans": ["局部马尔可夫性"]}, {"name": "Local Minima", "trans": ["局部极小"]}, {"name": "Local Minimum", "trans": ["局部极小"]}, {"name": "Local Representation", "trans": ["局部式表示/局部式表征"]}, {"name": "Log Likelihood", "trans": ["对数似然函数"]}, {"name": "Log Linear Model", "trans": ["对数线性模型"]}, {"name": "Log-Likelihood", "trans": ["对数似然"]}, {"name": "Log-Linear Regression", "trans": ["对数线性回归"]}, {"name": "Logistic Function", "trans": ["对数几率函数"]}, {"name": "Logistic Regression", "trans": ["对数几率回归"]}, {"name": "Logit", "trans": ["对数几率"]}, {"name": "Long Short Term Memory", "trans": ["长短期记忆"]}, {"name": "Long Short-Term Memory Network", "trans": ["长短期记忆网络"]}, {"name": "Loopy Belief Propagation", "trans": ["环状信念传播"]}, {"name": "Loss Function", "trans": ["损失函数"]}, {"name": "Low Rank Matrix Approximation", "trans": ["低秩矩阵近似"]}, {"name": "Machine Learning", "trans": ["机器学习"]}, {"name": "Macron-R", "trans": ["宏查全率"]}, {"name": "Manhattan Distance", "trans": ["曼哈顿距离"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["流形"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["流形假设"]}, {"name": "Manifold Learning", "trans": ["流形学习"]}, {"name": "<PERSON><PERSON>", "trans": ["间隔"]}, {"name": "Marginal Distribution", "trans": ["边缘分布"]}, {"name": "Marginal Independence", "trans": ["边缘独立性"]}, {"name": "Marginalization", "trans": ["边缘化"]}, {"name": "<PERSON><PERSON>", "trans": ["马尔可夫链"]}, {"name": "Markov Chain Monte Carlo", "trans": ["马尔可夫链蒙特卡罗"]}, {"name": "Markov Decision Process", "trans": ["马尔可夫决策过程"]}, {"name": "Markov Network", "trans": ["马尔可夫网络"]}, {"name": "Markov Process", "trans": ["马尔可夫过程"]}, {"name": "<PERSON><PERSON> Random Field", "trans": ["马尔可夫随机场"]}, {"name": "Mask", "trans": ["掩码"]}, {"name": "Matrix", "trans": ["矩阵"]}, {"name": "Matrix Inversion", "trans": ["逆矩阵"]}, {"name": "Max Pooling", "trans": ["最大汇聚"]}, {"name": "Maximal Clique", "trans": ["最大团"]}, {"name": "Maximum Entropy Model", "trans": ["最大熵模型"]}, {"name": "Maximum Likelihood Estimation", "trans": ["极大似然估计"]}, {"name": "Maximum Margin", "trans": ["最大间隔"]}, {"name": "Mean Filed", "trans": ["平均场"]}, {"name": "Mean Pooling", "trans": ["平均汇聚"]}, {"name": "Mean Squared Error", "trans": ["均方误差"]}, {"name": "Mean-Field", "trans": ["平均场"]}, {"name": "Memory Network", "trans": ["记忆网络"]}, {"name": "Message Passing", "trans": ["消息传递"]}, {"name": "Metric Learning", "trans": ["度量学习"]}, {"name": "Micro-R", "trans": ["微查全率"]}, {"name": "Minibatch", "trans": ["小批量"]}, {"name": "Minimal Description Length", "trans": ["最小描述长度"]}, {"name": "Minimax Game", "trans": ["极小极大博弈"]}, {"name": "<PERSON>kowski Distance", "trans": ["闵可夫斯基距离"]}, {"name": "Mixture of Experts", "trans": ["混合专家模型"]}, {"name": "Mixture-of-Gaussian", "trans": ["高斯混合"]}, {"name": "Model", "trans": ["模型"]}, {"name": "Model Selection", "trans": ["模型选择"]}, {"name": "Momentum Method", "trans": ["动量法"]}, {"name": "Monte Carlo Method", "trans": ["蒙特卡罗方法"]}, {"name": "Moral Graph", "trans": ["端正图/道德图"]}, {"name": "Moralization", "trans": ["道德化"]}, {"name": "Multi-Class Classification", "trans": ["多分类"]}, {"name": "Multi-Head Attention", "trans": ["多头注意力"]}, {"name": "Multi-Head Self-Attention", "trans": ["多头自注意力"]}, {"name": "Multi-Kernel Learning", "trans": ["多核学习"]}, {"name": "Multi-Label Learning", "trans": ["多标记学习"]}, {"name": "Multi-Layer Feedforward Neural Networks", "trans": ["多层前馈神经网络"]}, {"name": "Multi-Layer Perceptron", "trans": ["多层感知机"]}, {"name": "Multinomial Distribution", "trans": ["多项分布"]}, {"name": "Multiple Dimensional Scaling", "trans": ["多维缩放"]}, {"name": "Multiple Linear Regression", "trans": ["多元线性回归"]}, {"name": "Multitask Learning", "trans": ["多任务学习"]}, {"name": "Multivariate Normal Distribution", "trans": ["多元正态分布"]}, {"name": "Mutual Information", "trans": ["互信息"]}, {"name": "N-Gram Model", "trans": ["N元模型"]}, {"name": "Naive Bayes Classifier", "trans": ["朴素贝叶斯分类器"]}, {"name": "<PERSON><PERSON>", "trans": ["朴素贝叶斯"]}, {"name": "Nearest Neighbor Classifier", "trans": ["最近邻分类器"]}, {"name": "Negative Log Likelihood", "trans": ["负对数似然函数"]}, {"name": "Neighbourhood Component Analysis", "trans": ["近邻成分分析"]}, {"name": "Net Input", "trans": ["净输入"]}, {"name": "Neural Network", "trans": ["神经网络"]}, {"name": "Neural Turing Machine", "trans": ["神经图灵机"]}, {"name": "Neuron", "trans": ["神经元"]}, {"name": "Newton Method", "trans": ["牛顿法"]}, {"name": "No Free Lunch Theorem", "trans": ["没有免费午餐定理"]}, {"name": "Noise-Contrastive Estimation", "trans": ["噪声对比估计"]}, {"name": "Nominal Attribute", "trans": ["列名属性"]}, {"name": "Non-Convex Optimization", "trans": ["非凸优化"]}, {"name": "Non-Metric Distance", "trans": ["非度量距离"]}, {"name": "Non-Negative Matrix Factorization", "trans": ["非负矩阵分解"]}, {"name": "Non-Ordinal Attribute", "trans": ["无序属性"]}, {"name": "Norm", "trans": ["范数"]}, {"name": "Normal Distribution", "trans": ["正态分布"]}, {"name": "Normalization", "trans": ["规范化"]}, {"name": "Nuclear Norm", "trans": ["核范数"]}, {"name": "Number of Epochs", "trans": ["轮数"]}, {"name": "Numerical Attribute", "trans": ["数值属性"]}, {"name": "Object Detection", "trans": ["目标检测"]}, {"name": "Oblique Decision Tree", "trans": ["斜决策树"]}, {"name": "Occam's Razor", "trans": ["奥卡姆剃刀"]}, {"name": "Odds", "trans": ["几率"]}, {"name": "Off-Policy", "trans": ["异策略"]}, {"name": "On-Policy", "trans": ["同策略"]}, {"name": "One-Shot Learning", "trans": ["单试学习"]}, {"name": "One-Dependent Estimator", "trans": ["独依赖估计"]}, {"name": "One-Hot", "trans": ["独热"]}, {"name": "Online Learning", "trans": ["在线学习"]}, {"name": "Optimizer", "trans": ["优化器"]}, {"name": "Ordinal Attribute", "trans": ["有序属性"]}, {"name": "Orthogonal", "trans": ["正交"]}, {"name": "Orthogonal Matrix", "trans": ["正交矩阵"]}, {"name": "Out-Of-Bag Estimate", "trans": ["包外估计"]}, {"name": "Outlier", "trans": ["异常点"]}, {"name": "Over-Parameterized", "trans": ["过度参数化"]}, {"name": "Overfitting", "trans": ["过拟合"]}, {"name": "Oversampling", "trans": ["过采样"]}, {"name": "Pac-Learnable", "trans": ["PAC可学习"]}, {"name": "Padding", "trans": ["填充"]}, {"name": "Pairwise <PERSON>", "trans": ["成对马尔可夫性"]}, {"name": "Parallel Distributed Processing", "trans": ["分布式并行处理"]}, {"name": "Parameter", "trans": ["参数"]}, {"name": "Parameter Estimation", "trans": ["参数估计"]}, {"name": "Parameter Space", "trans": ["参数空间"]}, {"name": "Parameter Tuning", "trans": ["调参"]}, {"name": "Parametric ReLU", "trans": ["参数化修正线性单元/参数化整流线性单元"]}, {"name": "Part-Of-Speech Tagging", "trans": ["词性标注"]}, {"name": "Partial Derivative", "trans": ["偏导数"]}, {"name": "Partially Observable Markov Decision Processes", "trans": ["部分可观测马尔可夫决策过程"]}, {"name": "Partition Function", "trans": ["配分函数"]}, {"name": "Perceptron", "trans": ["感知机"]}, {"name": "Performance Measure", "trans": ["性能度量"]}, {"name": "Perplexity", "trans": ["困惑度"]}, {"name": "Pointer Network", "trans": ["指针网络"]}, {"name": "Policy", "trans": ["策略"]}, {"name": "Policy Gradient", "trans": ["策略梯度"]}, {"name": "Policy Iteration", "trans": ["策略迭代"]}, {"name": "Polynomial Kernel Function", "trans": ["多项式核函数"]}, {"name": "Pooling", "trans": ["汇聚"]}, {"name": "Pooling Layer", "trans": ["汇聚层"]}, {"name": "Positive Definite Matrix", "trans": ["正定矩阵"]}, {"name": "Post-Pruning", "trans": ["后剪枝"]}, {"name": "Potential Function", "trans": ["势函数"]}, {"name": "Power Method", "trans": ["幂法"]}, {"name": "Pre-Training", "trans": ["预训练"]}, {"name": "Precision", "trans": ["查准率/准确率"]}, {"name": "Prepruning", "trans": ["预剪枝"]}, {"name": "Primal Problem", "trans": ["主问题"]}, {"name": "Primary Visual Cortex", "trans": ["初级视觉皮层"]}, {"name": "Principal Component Analysis", "trans": ["主成分分析"]}, {"name": "Prior", "trans": ["先验"]}, {"name": "Probabilistic Context-Free Grammar", "trans": ["概率上下文无关文法"]}, {"name": "Probabilistic Graphical Model", "trans": ["概率图模型"]}, {"name": "Probabilistic Model", "trans": ["概率模型"]}, {"name": "Probability Density Function", "trans": ["概率密度函数"]}, {"name": "Probability Distribution", "trans": ["概率分布"]}, {"name": "Probably Approximately Correct", "trans": ["概率近似正确"]}, {"name": "Proposal Distribution", "trans": ["提议分布"]}, {"name": "Prototype-Based Clustering", "trans": ["原型聚类"]}, {"name": "Proximal Gradient Descent", "trans": ["近端梯度下降"]}, {"name": "Pruning", "trans": ["剪枝"]}, {"name": "Quadratic Loss Function", "trans": ["平方损失函数"]}, {"name": "Quadratic Programming", "trans": ["二次规划"]}, {"name": "<PERSON><PERSON><PERSON> Method", "trans": ["拟牛顿法"]}, {"name": "Radial Basis Function", "trans": ["径向基函数"]}, {"name": "Random Forest", "trans": ["随机森林"]}, {"name": "Random Sampling", "trans": ["随机采样"]}, {"name": "Random Search", "trans": ["随机搜索"]}, {"name": "Random Variable", "trans": ["随机变量"]}, {"name": "Random Walk", "trans": ["随机游走"]}, {"name": "Recall", "trans": ["查全率/召回率"]}, {"name": "Receptive Field", "trans": ["感受野"]}, {"name": "Reconstruction Error", "trans": ["重构误差"]}, {"name": "Rectified Linear Unit", "trans": ["修正线性单元/整流线性单元"]}, {"name": "Recurrent Neural Network", "trans": ["循环神经网络"]}, {"name": "Recursive Neural Network", "trans": ["递归神经网络"]}, {"name": "Regression", "trans": ["回归"]}, {"name": "Regularization", "trans": ["正则化"]}, {"name": "Regularizer", "trans": ["正则化项"]}, {"name": "Reinforcement Learning", "trans": ["强化学习"]}, {"name": "Relative Entropy", "trans": ["相对熵"]}, {"name": "Reparameterization", "trans": ["再参数化/重参数化"]}, {"name": "Representation", "trans": ["表示"]}, {"name": "Representation Learning", "trans": ["表示学习"]}, {"name": "Representer Theorem", "trans": ["表示定理"]}, {"name": "Reproducing Kernel Hilbert Space", "trans": ["再生核希尔伯特空间"]}, {"name": "Rescaling", "trans": ["再缩放"]}, {"name": "Reset Gate", "trans": ["重置门"]}, {"name": "Residual Connection", "trans": ["残差连接"]}, {"name": "Residual Network", "trans": ["残差网络"]}, {"name": "Restricted Boltzmann Machine", "trans": ["受限玻尔兹曼机"]}, {"name": "<PERSON><PERSON>", "trans": ["奖励"]}, {"name": "Ridge Regression", "trans": ["岭回归"]}, {"name": "Right Singular Vector", "trans": ["右奇异向量"]}, {"name": "Risk", "trans": ["风险"]}, {"name": "Rob<PERSON><PERSON>", "trans": ["稳健性"]}, {"name": "Root Node", "trans": ["根结点"]}, {"name": "Rule Learning", "trans": ["规则学习"]}, {"name": "Saddle Point", "trans": ["鞍点"]}, {"name": "<PERSON><PERSON>", "trans": ["样本"]}, {"name": "Sample Complexity", "trans": ["样本复杂度"]}, {"name": "Sample Space", "trans": ["样本空间"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["标量"]}, {"name": "Selective Ensemble", "trans": ["选择性集成"]}, {"name": "Self Information", "trans": ["自信息"]}, {"name": "Self-Attention", "trans": ["自注意力"]}, {"name": "Self-Organizing Map", "trans": ["自组织映射网"]}, {"name": "Self-Training", "trans": ["自训练"]}, {"name": "Semi-Definite Programming", "trans": ["半正定规划"]}, {"name": "Semi-Naive Bayes Classifiers", "trans": ["半朴素贝叶斯分类器"]}, {"name": "Semi-Restricted <PERSON> Machine", "trans": ["半受限玻尔兹曼机"]}, {"name": "Semi-Supervised Clustering", "trans": ["半监督聚类"]}, {"name": "Semi-Supervised Learning", "trans": ["半监督学习"]}, {"name": "Semi-Supervised Support Vector Machine", "trans": ["半监督支持向量机"]}, {"name": "Sentiment Analysis", "trans": ["情感分析"]}, {"name": "Separating Hyperplane", "trans": ["分离超平面"]}, {"name": "Sequential Covering", "trans": ["序贯覆盖"]}, {"name": "Sigmoid Belief Network", "trans": ["Sigmoid信念网络"]}, {"name": "Sigmoid Function", "trans": ["Sigmoid函数"]}, {"name": "Signed Distance", "trans": ["带符号距离"]}, {"name": "Similarity Measure", "trans": ["相似度度量"]}, {"name": "Simulated Annealing", "trans": ["模拟退火"]}, {"name": "Simultaneous Localization And Mapping", "trans": ["即时定位与地图构建"]}, {"name": "Singular Value", "trans": ["奇异值"]}, {"name": "Singular Value Decomposition", "trans": ["奇异值分解"]}, {"name": "<PERSON><PERSON>-Gram Model", "trans": ["跳元模型"]}, {"name": "Smoothing", "trans": ["平滑"]}, {"name": "Soft Margin", "trans": ["软间隔"]}, {"name": "Soft Margin Maximization", "trans": ["软间隔最大化"]}, {"name": "Softmax", "trans": ["Softmax/软最大化"]}, {"name": "Softmax Function", "trans": ["Softmax函数/软最大化函数"]}, {"name": "Softmax Regression", "trans": ["Softmax回归/软最大化回归"]}, {"name": "Softplus Function", "trans": ["Softplus函数"]}, {"name": "Span", "trans": ["张成子空间"]}, {"name": "Sparse Coding", "trans": ["稀疏编码"]}, {"name": "Sparse Representation", "trans": ["稀疏表示"]}, {"name": "Sparsity", "trans": ["稀疏性"]}, {"name": "Specialization", "trans": ["特化"]}, {"name": "Splitting Variable", "trans": ["切分变量"]}, {"name": "Squashing Function", "trans": ["挤压函数"]}, {"name": "Standard Normal Distribution", "trans": ["标准正态分布"]}, {"name": "State", "trans": ["状态"]}, {"name": "State Value Function", "trans": ["状态值函数"]}, {"name": "State-Action Value Function", "trans": ["状态-动作值函数"]}, {"name": "Stationary Distribution", "trans": ["平稳分布"]}, {"name": "Stationary Point", "trans": ["驻点"]}, {"name": "Statistical Learning", "trans": ["统计学习"]}, {"name": "Steepest Descent", "trans": ["最速下降法"]}, {"name": "Stochastic Gradient Descent", "trans": ["随机梯度下降"]}, {"name": "Stochastic Matrix", "trans": ["随机矩阵"]}, {"name": "Stochastic Process", "trans": ["随机过程"]}, {"name": "Stratified Sampling", "trans": ["分层采样"]}, {"name": "Stride", "trans": ["步幅"]}, {"name": "Structural Risk", "trans": ["结构风险"]}, {"name": "Structural Risk Minimization", "trans": ["结构风险最小化"]}, {"name": "Subsample", "trans": ["子采样"]}, {"name": "Subsampling", "trans": ["下采样"]}, {"name": "Subset Search", "trans": ["子集搜索"]}, {"name": "Subspace", "trans": ["子空间"]}, {"name": "Supervised Learning", "trans": ["监督学习"]}, {"name": "Support Vector", "trans": ["支持向量"]}, {"name": "Support Vector Expansion", "trans": ["支持向量展式"]}, {"name": "Support Vector Machine", "trans": ["支持向量机"]}, {"name": "Surrogat Loss", "trans": ["替代损失"]}, {"name": "Surrogate Function", "trans": ["替代函数"]}, {"name": "Surrogate Loss Function", "trans": ["代理损失函数"]}, {"name": "Symbolism", "trans": ["符号主义"]}, {"name": "Tangent Propagation", "trans": ["正切传播"]}, {"name": "Teacher Forcing", "trans": ["强制教学"]}, {"name": "Temporal-Difference Learning", "trans": ["时序差分学习"]}, {"name": "Tensor", "trans": ["张量"]}, {"name": "Test Error", "trans": ["测试误差"]}, {"name": "Test Sample", "trans": ["测试样本"]}, {"name": "Test Set", "trans": ["测试集"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "trans": ["阈值"]}, {"name": "Threshold Logic Unit", "trans": ["阈值逻辑单元"]}, {"name": "Threshold-Moving", "trans": ["阈值移动"]}, {"name": "Tied Weight", "trans": ["捆绑权重"]}, {"name": "Tikhonov Regularization", "trans": ["<PERSON><PERSON><PERSON><PERSON>正则化"]}, {"name": "Time Delay Neural Network", "trans": ["时延神经网络"]}, {"name": "Time Homogenous <PERSON><PERSON>", "trans": ["时间齐次马尔可夫链"]}, {"name": "Time Step", "trans": ["时间步"]}, {"name": "Token", "trans": ["词元"]}, {"name": "Tokenize", "trans": ["词元化"]}, {"name": "Tokenization", "trans": ["词元化"]}, {"name": "Tokenizer", "trans": ["词元分析器"]}, {"name": "Topic Model", "trans": ["话题模型"]}, {"name": "Topic Modeling", "trans": ["话题分析"]}, {"name": "Trace", "trans": ["迹"]}, {"name": "Training", "trans": ["训练"]}, {"name": "Training Error", "trans": ["训练误差"]}, {"name": "Training Sample", "trans": ["训练样本"]}, {"name": "Training Set", "trans": ["训练集"]}, {"name": "Transductive Learning", "trans": ["直推学习"]}, {"name": "Transductive Transfer Learning", "trans": ["直推迁移学习"]}, {"name": "Transfer Learning", "trans": ["迁移学习"]}, {"name": "Transformer", "trans": ["Transformer"]}, {"name": "Transformer Model", "trans": ["Transformer模型"]}, {"name": "Transpose", "trans": ["转置"]}, {"name": "Transposed Convolution", "trans": ["转置卷积"]}, {"name": "Trial And Error", "trans": ["试错"]}, {"name": "Trigram", "trans": ["三元语法"]}, {"name": "Turing Machine", "trans": ["图灵机"]}, {"name": "Underfitting", "trans": ["欠拟合"]}, {"name": "Undersampling", "trans": ["欠采样"]}, {"name": "Undirected Graphical Model", "trans": ["无向图模型"]}, {"name": "Uniform Distribution", "trans": ["均匀分布"]}, {"name": "Unigram", "trans": ["一元语法"]}, {"name": "Unit", "trans": ["单元"]}, {"name": "Universal Approximation Theorem", "trans": ["通用近似定理"]}, {"name": "Universal Approximator", "trans": ["通用近似器"]}, {"name": "Universal Function Approximator", "trans": ["通用函数近似器"]}, {"name": "Unknown To<PERSON>", "trans": ["未知词元"]}, {"name": "Unsupervised Layer-Wise Training", "trans": ["无监督逐层训练"]}, {"name": "Unsupervised Learning", "trans": ["无监督学习"]}, {"name": "Update Gate", "trans": ["更新门"]}, {"name": "Upsampling", "trans": ["上采样"]}, {"name": "V-Structure", "trans": ["V型结构"]}, {"name": "Validation Set", "trans": ["验证集"]}, {"name": "Validity Index", "trans": ["有效性指标"]}, {"name": "Value Function Approximation", "trans": ["值函数近似"]}, {"name": "Value Iteration", "trans": ["值迭代"]}, {"name": "Vanishing Gradient Problem", "trans": ["梯度消失问题"]}, {"name": "Vapnik-Chervonenkis Dimension", "trans": ["VC维"]}, {"name": "Variable Elimination", "trans": ["变量消去"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["方差"]}, {"name": "Variational Autoencoder", "trans": ["变分自编码器"]}, {"name": "Variational Inference", "trans": ["变分推断"]}, {"name": "Vector", "trans": ["向量"]}, {"name": "Vector Space Model", "trans": ["向量空间模型"]}, {"name": "Version Space", "trans": ["版本空间"]}, {"name": "Viterbi Algorithm", "trans": ["维特比算法"]}, {"name": "Vocabulary", "trans": ["词表"]}, {"name": "Warp", "trans": ["线程束"]}, {"name": "<PERSON><PERSON>", "trans": ["弱学习器"]}, {"name": "Weakly Supervised Learning", "trans": ["弱监督学习"]}, {"name": "Weight", "trans": ["权重"]}, {"name": "Weight Decay", "trans": ["权重衰减"]}, {"name": "Weight Sharing", "trans": ["权共享"]}, {"name": "Weighted Voting", "trans": ["加权投票"]}, {"name": "Whitening", "trans": ["白化"]}, {"name": "Winner-Take-All", "trans": ["胜者通吃"]}, {"name": "Within-Class Scatter Matrix", "trans": ["类内散度矩阵"]}, {"name": "Word Embedding", "trans": ["词嵌入"]}, {"name": "Word Sense Disambiguation", "trans": ["词义消歧"]}, {"name": "Word Vector", "trans": ["词向量"]}, {"name": "Zero Padding", "trans": ["零填充"]}, {"name": "Zero-Shot Learning", "trans": ["零试学习"]}, {"name": "<PERSON><PERSON><PERSON>'s Law", "trans": ["齐普夫定律"]}]