{"build": {"beforeBuildCommand": "yarn build", "beforeDevCommand": "yarn run dev", "devPath": "http://localhost:3000", "distDir": "../build"}, "package": {"productName": "qwerty-learner", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "deb": {"depends": []}, "externalBin": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.litongjava.qwerty.learner", "longDescription": "", "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}, "resources": [], "shortDescription": "", "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"fullscreen": false, "height": 600, "resizable": true, "title": "qwerty-learner", "width": 800}]}}