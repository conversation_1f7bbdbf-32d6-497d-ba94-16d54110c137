{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "css.lint.unknownAtRules": "ignore", "css.validate": false, "files.eol": "\n", "search.exclude": {"**/public/dicts/": true, "**/assets/CET4_T.json": true}, "cSpell.words": ["alipay", "compat", "<PERSON><PERSON>", "esae", "fontawesome", "fortawesome", "headlessui", "heroicons", "IELTS", "immer", "Majesticons", "pako", "r<PERSON><PERSON>", "svgr", "tabler", "tada", "tailwindcss", "trivago", "ukphone", "ungzip", "usphone", "vercel", "Wechat", "<PERSON><PERSON>", "wordlist", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true}