{"name": "qwerty-learner", "version": "0.1.0", "private": true, "homepage": ".", "dependencies": {"@floating-ui/react": "^0.20.1", "@headlessui/react": "^1.7.13", "@headlessui/tailwindcss": "^0.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-progress": "^1.0.2", "@radix-ui/react-radio-group": "^1.1.2", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slider": "^1.1.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-table": "^8.10.7", "@vercel/analytics": "^1.5.0", "animate.css": "^4.1.1", "canvas-confetti": "^1.6.0", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "clsx": "^2.0.0", "dayjs": "^1.11.8", "dexie": "^3.2.3", "dexie-export-import": "^4.0.7", "dexie-react-hooks": "^1.1.3", "echarts": "^5.4.2", "embla-carousel-react": "^8.2.1", "file-saver": "^2.0.5", "howler": "^2.2.3", "html-to-image": "^1.11.11", "immer": "^9.0.21", "jotai": "^2.0.3", "lucide-react": "^0.294.0", "mixpanel-browser": "^2.45.0", "pako": "^2.1.0", "react": "^18.2.0", "react-activity-calendar": "^2.0.2", "react-app-polyfill": "^3.0.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.3.7", "react-router-dom": "^6.8.2", "react-timer-hook": "^3.0.5", "react-tooltip": "^5.18.0", "source-map-explorer": "^2.5.2", "swr": "^2.0.4", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "typescript": "^4.0.3", "use-immer": "^0.9.0", "use-sound": "^4.0.1", "xlsx": "^0.18.5"}, "scripts": {"dev": "vite", "start": "vite", "build": "cross-env CI=false vite build --base=./", "test": "echo \"No tests\"", "test:e2e": "playwright test", "lint": "eslint .", "prettier": "prettier --write .", "prepare": "husky install"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@iconify/json": "^2.2.242", "@playwright/test": "^1.40.1", "@svgr/core": "^7.0.0", "@svgr/plugin-jsx": "^7.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/postcss7-compat": "^2.2.17", "@trivago/prettier-plugin-sort-imports": "^4.1.1", "@types/canvas-confetti": "^1.6.0", "@types/echarts": "^4.9.18", "@types/file-saver": "^2.0.5", "@types/gtag.js": "^0.0.20", "@types/howler": "^2.2.3", "@types/mixpanel-browser": "^2.38.1", "@types/node": "18.14.6", "@types/pako": "^2.0.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.1.7", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "git-last-commit": "^1.0.1", "husky": "^8.0.0", "lint-staged": "^13.1.2", "postcss": "^8.4.21", "prettier": "^2.8.4", "prettier-plugin-tailwindcss": "^0.2.7", "rollup-plugin-visualizer": "^5.9.0", "tailwindcss": "^3.3.1", "typescript-plugin-css-modules": "^5.0.1", "unplugin-icons": "^0.16.1", "vite": "^4.1.1"}}